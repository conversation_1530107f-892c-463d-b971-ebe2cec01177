
> wizlop@0.1.0 build:webpack
> webpack --config config/webpack/webpack.dev.js

asset bundle.js 4.45 MiB [compared for emit] [big] (name: main)
runtime modules 1.04 KiB 5 modules
modules by path ./node_modules/ 1.48 MiB 109 modules
modules by path ./app/ 107 KiB
  modules by path ./app/landing/components/ 85.6 KiB
    modules by path ./app/landing/components/hero/*.tsx 36.2 KiB 4 modules
    modules by path ./app/landing/components/features/*.tsx 42.1 KiB 3 modules
    + 3 modules
  ./app/page.tsx 1.13 KiB [built] [code generated]
  ./app/colors.ts 3.48 KiB [built] [code generated]
  ./app/shared/poi/constants.ts 12.1 KiB [built] [code generated]
  ./app/landing/utils/responsiveUtils.ts 5.08 KiB [built] [code generated]

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/api/pois/globe/route.ts
3:9-20
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/api/pois/globe/route.ts(3,10)
      TS2459: Module '"@/app/api/auth/[...nextauth]/route"' declares 'authOptions' locally, but it is not exported.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/api/pois/globe/route.ts(3,10)
      TS2459: Module '"@/app/api/auth/[...nextauth]/route"' declares 'authOptions' locally, but it is not exported.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/api/pois/globe/route.ts
83:6-18
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/api/pois/globe/route.ts(83,7)
      TS2345: Argument of type '{ poi_type: string; poi_id: unknown; }[]' is not assignable to parameter of type '{ poi_id: number; poi_type: string; }[]'.
  Type '{ poi_type: string; poi_id: unknown; }' is not assignable to type '{ poi_id: number; poi_type: string; }'.
    Types of property 'poi_id' are incompatible.
      Type 'unknown' is not assignable to type 'number'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/api/pois/globe/route.ts(83,7)
      TS2345: Argument of type '{ poi_type: string; poi_id: unknown; }[]' is not assignable to parameter of type '{ poi_id: number; poi_type: string; }[]'.
  Type '{ poi_type: string; poi_id: unknown; }' is not assignable to type '{ poi_id: number; poi_type: string; }'.
    Types of property 'poi_id' are incompatible.
      Type 'unknown' is not assignable to type 'number'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/chat/components/input-area/InputArea.tsx
122:83-90
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/chat/components/input-area/InputArea.tsx(122,84)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/chat/components/input-area/InputArea.tsx(122,84)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/chat/components/input-area/InputArea.tsx
156:50-57
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/chat/components/input-area/InputArea.tsx(156,51)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/chat/components/input-area/InputArea.tsx(156,51)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/chat/components/ui/IconButton.tsx
66:36-43
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/chat/components/ui/IconButton.tsx(66,37)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/chat/components/ui/IconButton.tsx(66,37)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/globe/components/POIRankingPanel.tsx
549:9-24
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/globe/components/POIRankingPanel.tsx(549,10)
      TS2322: Type 'InteractionData | null' is not assignable to type 'POIInteractionData | null | undefined'.
  Property 'media_count' is missing in type 'InteractionData' but required in type 'POIInteractionData'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/globe/components/POIRankingPanel.tsx(549,10)
      TS2322: Type 'InteractionData | null' is not assignable to type 'POIInteractionData | null | undefined'.
  Property 'media_count' is missing in type 'InteractionData' but required in type 'POIInteractionData'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/AIPlaygroundDemo.tsx
314:7-8
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/AIPlaygroundDemo.tsx(314,8)
      TS1381: Unexpected token. Did you mean `{'}'}` or `&rbrace;`?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/AIPlaygroundDemo.tsx(314,8)
      TS1381: Unexpected token. Did you mean `{'}'}` or `&rbrace;`?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/AIPlaygroundDemo.tsx
257:84-91
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/AIPlaygroundDemo.tsx(257,85)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/AIPlaygroundDemo.tsx(257,85)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/AIShowcase.tsx
./app/landing/components/features/AIShowcase.tsx 188:24-31
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/AIShowcase.tsx(188,25)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/AIShowcase.tsx(188,25)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 10:0-62 10:0-62
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/DynamicCategoryMosaic.tsx
./app/landing/components/features/DynamicCategoryMosaic.tsx 477:5-10
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/DynamicCategoryMosaic.tsx(477,6)
      TS17001: JSX elements cannot have multiple attributes with the same name.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/DynamicCategoryMosaic.tsx(477,6)
      TS17001: JSX elements cannot have multiple attributes with the same name.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 12:0-84 12:0-84
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/DynamicCategoryMosaic.tsx
./app/landing/components/features/DynamicCategoryMosaic.tsx 514:6-11
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/DynamicCategoryMosaic.tsx(514,7)
      TS17001: JSX elements cannot have multiple attributes with the same name.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/DynamicCategoryMosaic.tsx(514,7)
      TS17001: JSX elements cannot have multiple attributes with the same name.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 12:0-84 12:0-84
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx
./app/landing/components/hero/HeroPreview.tsx 85:19-56
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(85,20)
      TS7053: Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ mobile: string | number; tablet: string | number; desktop: string | number; }'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(85,20)
      TS7053: Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ mobile: string | number; tablet: string | number; desktop: string | number; }'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 7:0-60 7:0-60
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx
./app/landing/components/hero/HeroPreview.tsx 85:45-55
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(85,46)
      TS2304: Cannot find name 'screenSize'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(85,46)
      TS2304: Cannot find name 'screenSize'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 7:0-60 7:0-60
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx
./app/landing/components/hero/HeroPreview.tsx 86:20-58
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(86,21)
      TS7053: Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ mobile: string | number; tablet: string | number; desktop: string | number; }'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(86,21)
      TS7053: Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ mobile: string | number; tablet: string | number; desktop: string | number; }'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 7:0-60 7:0-60
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx
./app/landing/components/hero/HeroPreview.tsx 86:47-57
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(86,48)
      TS2304: Cannot find name 'screenSize'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(86,48)
      TS2304: Cannot find name 'screenSize'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 7:0-60 7:0-60
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx
./app/landing/components/hero/HeroPreview.tsx 88:3-13
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(88,4)
      TS2304: Cannot find name 'screenSize'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(88,4)
      TS2304: Cannot find name 'screenSize'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 7:0-60 7:0-60
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx
./app/landing/components/hero/HeroPreview.tsx 90:6-16
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(90,7)
      TS2304: Cannot find name 'screenSize'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(90,7)
      TS2304: Cannot find name 'screenSize'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 7:0-60 7:0-60
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx
./app/landing/components/hero/HeroPreview.tsx 99:4-14
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(99,5)
      TS2304: Cannot find name 'screenSize'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(99,5)
      TS2304: Cannot find name 'screenSize'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 7:0-60 7:0-60
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx
./app/landing/components/hero/HeroPreview.tsx 101:7-17
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(101,8)
      TS2304: Cannot find name 'screenSize'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(101,8)
      TS2304: Cannot find name 'screenSize'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 7:0-60 7:0-60
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx
./app/landing/components/hero/HeroPreview.tsx 104:12-22
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(104,13)
      TS2304: Cannot find name 'screenSize'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(104,13)
      TS2304: Cannot find name 'screenSize'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 7:0-60 7:0-60
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx
./app/landing/components/hero/HeroPreview.tsx 132:14-24
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(132,15)
      TS2304: Cannot find name 'screenSize'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(132,15)
      TS2304: Cannot find name 'screenSize'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 7:0-60 7:0-60
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx
./app/landing/components/hero/HeroPreview.tsx 133:15-25
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(133,16)
      TS2304: Cannot find name 'screenSize'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/hero/HeroPreview.tsx(133,16)
      TS2304: Cannot find name 'screenSize'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 7:0-60 7:0-60
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx
504:10-25
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx(504,11)
      TS2322: Type 'InteractionData | { poi_id: number; poi_type: string; like_count: any; favorite_count: any; visit_count: any; media_count: number; user_has_liked: any; user_has_favorited: any; user_has_visited: any; } | null' is not assignable to type 'POIInteractionData | null | undefined'.
  Property 'media_count' is missing in type 'InteractionData' but required in type 'POIInteractionData'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx(504,11)
      TS2322: Type 'InteractionData | { poi_id: number; poi_type: string; like_count: any; favorite_count: any; visit_count: any; media_count: number; user_has_liked: any; user_has_favorited: any; user_has_visited: any; } | null' is not assignable to type 'POIInteractionData | null | undefined'.
  Property 'media_count' is missing in type 'InteractionData' but required in type 'POIInteractionData'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx
510:19-29
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx(510,20)
      TS2339: Property 'like_count' does not exist on type 'POI'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx(510,20)
      TS2339: Property 'like_count' does not exist on type 'POI'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx
514:29-39
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx(514,30)
      TS2339: Property 'like_count' does not exist on type 'POI'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx(514,30)
      TS2339: Property 'like_count' does not exist on type 'POI'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx
515:33-47
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx(515,34)
      TS2339: Property 'favorite_count' does not exist on type 'POI'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx(515,34)
      TS2339: Property 'favorite_count' does not exist on type 'POI'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx
516:30-41
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx(516,31)
      TS2339: Property 'visit_count' does not exist on type 'POI'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx(516,31)
      TS2339: Property 'visit_count' does not exist on type 'POI'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx
518:33-47
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx(518,34)
      TS2339: Property 'user_has_liked' does not exist on type 'POI'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx(518,34)
      TS2339: Property 'user_has_liked' does not exist on type 'POI'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx
519:37-55
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx(519,38)
      TS2339: Property 'user_has_favorited' does not exist on type 'POI'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx(519,38)
      TS2339: Property 'user_has_favorited' does not exist on type 'POI'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx
520:35-51
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx(520,36)
      TS2339: Property 'user_has_visited' does not exist on type 'POI'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/pois/page.tsx(520,36)
      TS2339: Property 'user_has_visited' does not exist on type 'POI'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/settings/page.tsx
123:17-24
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/settings/page.tsx(123,18)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/settings/page.tsx(123,18)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/cards/components/LocationHoverCard.tsx
196:59-66
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/cards/components/LocationHoverCard.tsx(196,60)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/cards/components/LocationHoverCard.tsx(196,60)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/locationManager/components/LocationSetup.tsx
293:66-73
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/locationManager/components/LocationSetup.tsx(293,67)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/locationManager/components/LocationSetup.tsx(293,67)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/poi/components/POIFilter.tsx
311:38-45
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/poi/components/POIFilter.tsx(311,39)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/poi/components/POIFilter.tsx(311,39)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/poi/components/POIFilter.tsx
417:31-38
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/poi/components/POIFilter.tsx(417,32)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/poi/components/POIFilter.tsx(417,32)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/poi/components/POIFilter.tsx
439:32-39
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/poi/components/POIFilter.tsx(439,33)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/poi/components/POIFilter.tsx(439,33)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/poi/components/POIFilter.tsx
710:31-38
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/poi/components/POIFilter.tsx(710,32)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/poi/components/POIFilter.tsx(710,32)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/poi/components/POIMarkers.tsx
386:30-37
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/poi/components/POIMarkers.tsx(386,31)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/poi/components/POIMarkers.tsx(386,31)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/favorites/index.ts
5:14-61
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/favorites/index.ts(5,15)
      TS2307: Cannot find module '@/app/shared/userInteractions/favorites/hooks' or its corresponding type declarations.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/favorites/index.ts(5,15)
      TS2307: Cannot find module '@/app/shared/userInteractions/favorites/hooks' or its corresponding type declarations.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/reviews/components/ReviewWriteModal.tsx
242:40-47
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/reviews/components/ReviewWriteModal.tsx(242,41)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/reviews/components/ReviewWriteModal.tsx(242,41)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/reviews/components/ReviewWriteModal.tsx
260:40-47
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/reviews/components/ReviewWriteModal.tsx(260,41)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/reviews/components/ReviewWriteModal.tsx(260,41)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/reviews/components/ReviewWriteModal.tsx
283:40-47
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/reviews/components/ReviewWriteModal.tsx(283,41)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/reviews/components/ReviewWriteModal.tsx(283,41)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/reviews/components/ReviewWriteModal.tsx
318:41-48
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/reviews/components/ReviewWriteModal.tsx(318,42)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/reviews/components/ReviewWriteModal.tsx(318,42)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/reviews/components/ReviewWriteModal.tsx
337:32-39
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/reviews/components/ReviewWriteModal.tsx(337,33)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/reviews/components/ReviewWriteModal.tsx(337,33)
      TS2551: Property 'gray300' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'. Did you mean 'gray100'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/shared/components/UserInteractionTab.tsx
104:31-38
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/shared/components/UserInteractionTab.tsx(104,32)
      TS2339: Property 'navy100' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/shared/components/UserInteractionTab.tsx(104,32)
      TS2339: Property 'navy100' does not exist on type '{ readonly blue50: "#F0FAFF"; readonly blue100: "#E1F5FF"; readonly blue200: "#B3E5FF"; readonly navy50: "#F8F8FB"; readonly green50: "#F5FDF8"; readonly green100: "#EBFBF0"; readonly green200: "#D7F7E1"; ... 4 more ...; readonly gray500: "#6B7280"; }'.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/visits/index.ts
5:14-58
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/visits/index.ts(5,15)
      TS2307: Cannot find module '@/app/shared/userInteractions/visits/hooks' or its corresponding type declarations.
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/shared/userInteractions/visits/index.ts(5,15)
      TS2307: Cannot find module '@/app/shared/userInteractions/visits/hooks' or its corresponding type declarations.
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:166:62)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/HookCodeFactory.js:33:10), <anonymous>:10:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/tapable/lib/Hook.js:20:14)

webpack 5.100.1 compiled with 46 errors in 3514 ms
