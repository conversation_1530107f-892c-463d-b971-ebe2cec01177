/** @format */

'use client';

import { FILTER_RESPONSIVE_CONFIG } from '@/app/landing/utils/responsiveUtils';
import React, { useState } from 'react';
import {
	FiActivity,
	FiBook,
	FiCoffee,
	FiHeart,
	FiMapPin,
	FiMusic,
	FiShoppingBag,
	FiSun,
	FiTrendingUp,
} from 'react-icons/fi';
import { colors } from '../../../colors';
import { POI_CATEGORIES_DATA } from '../../../shared/poi/constants';

// All available colors from design system (brand + supporting)
const ALL_COLORS = [
	colors.brand.blue,
	colors.brand.green,
	colors.brand.navy,
	colors.supporting.lightBlue,
	colors.supporting.mintGreen,
	colors.supporting.teal,
	colors.supporting.darkBlue,
	colors.supporting.purple,
	colors.supporting.softNavy,
];

// Helper functions from constants
const getPOICategories = (): string[] => {
	return Object.keys(POI_CATEGORIES_DATA);
};

const getPOISubcategoriesWithCategory = () => {
	const allSubcategories: Array<{ subcategory: string; category: string }> = [];
	for (const category in POI_CATEGORIES_DATA) {
		const categoryData =
			POI_CATEGORIES_DATA[category as keyof typeof POI_CATEGORIES_DATA];
		for (const subcategory of categoryData.subcategories) {
			allSubcategories.push({ subcategory, category });
		}
	}
	return allSubcategories;
};

// Generates a gradient from a base color with 50% transparency
const getGradient = (color: string) => {
	const hexToRgb = (hex: string) => {
		const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
		return result
			? {
					r: parseInt(result[1], 16),
					g: parseInt(result[2], 16),
					b: parseInt(result[3], 16),
			  }
			: null;
	};

	const baseRgb = hexToRgb(color);
	if (!baseRgb) return color;

	// Create a lighter version for gradient end
	const lighterRgb = {
		r: Math.min(255, baseRgb.r + 40),
		g: Math.min(255, baseRgb.g + 40),
		b: Math.min(255, baseRgb.b + 40),
	};

	const startColor = `rgba(${baseRgb.r}, ${baseRgb.g}, ${baseRgb.b}, 0.5)`;
	const endColor = `rgba(${lighterRgb.r}, ${lighterRgb.g}, ${lighterRgb.b}, 0.5)`;

	return `linear-gradient(45deg, ${startColor}, ${endColor})`;
};

// Shuffle function
const shuffleArray = <T,>(array: T[]): T[] => {
	const shuffled = [...array];
	for (let i = shuffled.length - 1; i > 0; i--) {
		const j = Math.floor(Math.random() * (i + 1));
		[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
	}
	return shuffled;
};

interface CardData {
	subcategory: string;
	category: string;
	weight?: number;
	x?: number;
	y?: number;
	width?: number;
	height?: number;
}

interface DynamicCategoryMosaicProps {
	onCategorySelect?: (category: CardData) => void;
}

// Treemap algorithm implementation
const generateTreemap = (
	data: CardData[],
	x: number,
	y: number,
	width: number,
	height: number
): CardData[] => {
	if (data.length === 0) return [];
	if (data.length === 1) {
		return [{ ...data[0], x, y, width, height }];
	}

	// Calculate total weight
	const totalWeight = data.reduce((sum, item) => sum + (item.weight || 1), 0);

	// Sort by weight (largest first)
	const sortedData = [...data].sort(
		(a, b) => (b.weight || 1) - (a.weight || 1)
	);

	// Split into two groups
	let leftWeight = 0;
	let splitIndex = 0;
	const targetWeight = totalWeight / 2;

	for (let i = 0; i < sortedData.length; i++) {
		leftWeight += sortedData[i].weight || 1;
		if (leftWeight >= targetWeight) {
			splitIndex = i + 1;
			break;
		}
	}

	const leftGroup = sortedData.slice(0, splitIndex);
	const rightGroup = sortedData.slice(splitIndex);

	// Decide split direction based on aspect ratio
	const isWiderThanTall = width > height;
	let leftResult: CardData[] = [];
	let rightResult: CardData[] = [];

	if (isWiderThanTall) {
		// Split vertically
		const leftWidth = (width * leftWeight) / totalWeight;
		const rightWidth = width - leftWidth;

		leftResult = generateTreemap(leftGroup, x, y, leftWidth, height);
		rightResult = generateTreemap(
			rightGroup,
			x + leftWidth,
			y,
			rightWidth,
			height
		);
	} else {
		// Split horizontally
		const leftHeight = (height * leftWeight) / totalWeight;
		const rightHeight = height - leftHeight;

		leftResult = generateTreemap(leftGroup, x, y, width, leftHeight);
		rightResult = generateTreemap(
			rightGroup,
			x,
			y + leftHeight,
			width,
			rightHeight
		);
	}

	return [...leftResult, ...rightResult];
};

// Get icon for subcategory - dynamic based on category type
const getCardIcon = (_subcategory: string, category: string) => {
	// Map categories to icon types
	const categoryIconMap: { [key: string]: JSX.Element } = {
		'Food & Drink': <FiCoffee className='w-full h-full text-white' />,
		'Cultural & Creative Experiences': (
			<FiBook className='w-full h-full text-white' />
		),
		'Sports & Fitness': <FiActivity className='w-full h-full text-white' />,
		Entertainment: <FiMusic className='w-full h-full text-white' />,
		'Shopping & Markets': (
			<FiShoppingBag className='w-full h-full text-white' />
		),
		'Outdoor & Nature': <FiSun className='w-full h-full text-white' />,
		'Wellness & Beauty': <FiHeart className='w-full h-full text-white' />,
		Transportation: <FiTrendingUp className='w-full h-full text-white' />,
	};

	return (
		categoryIconMap[category] || (
			<FiMapPin className='w-full h-full text-white' />
		)
	);
};

const CategoryCard: React.FC<{
	cardData: CardData;
	index: number;
}> = ({ cardData, index }) => {
	const { x = 0, y = 0, width = 0, height = 0 } = cardData;

	// Get color ensuring no adjacent cards have same color
	const getDistributedColor = (cardIndex: number, totalCards: number) => {
		// Use a more sophisticated distribution to avoid adjacent duplicates
		const colorIndex =
			(cardIndex * 3 + Math.floor(cardIndex / 3)) % ALL_COLORS.length;
		return ALL_COLORS[colorIndex];
	};

	const color = getDistributedColor(index, 10); // Assuming max 10 cards
	const gradient = getGradient(color);

	// Add spacing between cards
	const spacing = 4;
	const adjustedX = x + spacing;
	const adjustedY = y + spacing;
	const adjustedWidth = width - spacing * 2;
	const adjustedHeight = height - spacing * 2;

	return (
		<div
			style={{
				position: 'absolute',
				left: `${adjustedX}px`,
				top: `${adjustedY}px`,
				width: `${adjustedWidth}px`,
				height: `${adjustedHeight}px`,
				background: gradient, // Full opacity for filled colors
				border: `2px solid ${color}99`, // 60% border opacity for subtle contrast
				borderRadius: '20px',
				boxSizing: 'border-box',
				overflow: 'hidden',
				color: 'white',
				display: 'flex',
				flexDirection: 'column',
				justifyContent: 'center',
				alignItems: 'center',
				padding: adjustedWidth > 180 ? '24px' : '16px',
				textAlign: 'center',
				textShadow: '2px 2px 6px rgba(0,0,0,0.6)',
				boxShadow: `0 6px 20px ${color}30, 0 2px 8px rgba(0,0,0,0.15)`,
				backdropFilter: 'blur(12px)',
			}}>
			{/* Icon with enhanced styling */}
			<div
				style={{
					marginBottom: adjustedWidth > 180 ? '20px' : '12px',
					fontSize:
						adjustedWidth > 250
							? '3em'
							: adjustedWidth > 180
							? '2.5em'
							: adjustedWidth > 120
							? '2em'
							: '1.5em',
					filter: 'drop-shadow(0 3px 6px rgba(0,0,0,0.4))',
				}}>
				{getCardIcon(cardData.subcategory, cardData.category)}
			</div>

			<h3
				style={{
					fontWeight: '900',
					fontSize:
						adjustedWidth > 250
							? '1.8em'
							: adjustedWidth > 180
							? '1.5em'
							: adjustedWidth > 120
							? '1.3em'
							: '1.1em',
					margin: 0,
					marginBottom: adjustedWidth > 180 ? '12px' : '8px',
					letterSpacing: '0.8px',
					lineHeight: '1.1',
					textTransform: 'uppercase',
				}}>
				{cardData.subcategory}
			</h3>

			<p
				style={{
					fontSize:
						adjustedWidth > 250
							? '1.2em'
							: adjustedWidth > 180
							? '1em'
							: adjustedWidth > 120
							? '0.9em'
							: '0.8em',
					opacity: 0.95,
					margin: 0,
					fontWeight: '600',
					letterSpacing: '0.4px',
					textTransform: 'capitalize',
				}}>
				{cardData.category}
			</p>
		</div>
	);
};

const DynamicCategoryMosaic: React.FC<DynamicCategoryMosaicProps> = ({
	onCategorySelect,
}) => {
	const [selectedCategory, setSelectedCategory] = useState<string>('all');
	const [selectedSubcategory, setSelectedSubcategory] = useState<string>('all');
	const [shuffledData, setShuffledData] = useState<
		Array<{ subcategory: string; category: string }>
	>([]);
	const [layoutCards, setLayoutCards] = useState<any[]>([]);
	const [containerSize, setContainerSize] = useState({
		width: 1200,
		height: 800,
	});
	const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>(
		'desktop'
	);
	const containerRef = React.useRef<HTMLDivElement>(null);

	// Get all categories and subcategories
	const allCategories = getPOICategories();
	const allSubcategories = getPOISubcategoriesWithCategory();

	// Initialize shuffled data on component mount (shuffle on refresh only)
	React.useEffect(() => {
		const shuffled = shuffleArray(allSubcategories);
		setShuffledData(shuffled);
	}, []); // Empty dependency array means this only runs on mount/refresh

	// Track screen size for responsive behavior
	React.useEffect(() => {
		const updateScreenSize = () => {
			const width = window.innerWidth;
			if (width < 768) {
				setScreenSize('mobile');
			} else if (width < 1024) {
				setScreenSize('tablet');
			} else {
				setScreenSize('desktop');
			}
		};

		updateScreenSize();
		window.addEventListener('resize', updateScreenSize);
		return () => window.removeEventListener('resize', updateScreenSize);
	}, []);

	// Track container size for responsive layout
	React.useEffect(() => {
		const updateContainerSize = () => {
			if (containerRef.current) {
				const rect = containerRef.current.getBoundingClientRect();
				const newWidth = Math.max(300, Math.min(1200, rect.width));
				const newHeight = Math.max(300, Math.min(800, rect.width * 0.67)); // Maintain aspect ratio
				setContainerSize({ width: newWidth, height: newHeight });
			}
		};

		// Initial size
		updateContainerSize();

		// Listen for resize events
		const resizeObserver = new ResizeObserver(updateContainerSize);
		if (containerRef.current) {
			resizeObserver.observe(containerRef.current);
		}

		// Listen for window resize as fallback
		window.addEventListener('resize', updateContainerSize);

		return () => {
			resizeObserver.disconnect();
			window.removeEventListener('resize', updateContainerSize);
		};
	}, []);

	// Filter data based on selected category and subcategory
	const getFilteredData = () => {
		let filtered = shuffledData;

		if (selectedCategory !== 'all') {
			filtered = filtered.filter((item) => item.category === selectedCategory);
		}

		if (selectedSubcategory !== 'all') {
			filtered = filtered.filter(
				(item) => item.subcategory === selectedSubcategory
			);
		}

		// Adjust card count based on container size
		const maxCards =
			containerSize.width < 600 ? 6 : containerSize.width < 900 ? 8 : 10;
		return filtered.slice(0, maxCards);
	};

	// Generate layout only when filtered data changes (not on every render)
	React.useEffect(() => {
		const filteredData = getFilteredData();

		if (filteredData.length > 0) {
			// Add varied weights for extreme size differences (minimum 5px difference)
			const subcategoriesWithWeights = filteredData.map((item, index) => {
				// Create extreme size variations with multiple mathematical functions
				const primaryFactor = Math.sin(index * 3.14159) * 6; // Large base variation
				const secondaryFactor = Math.cos(index * 2.1) * 4; // Secondary variation
				const tertiaryFactor = Math.sin(index * 1.3) * 3; // Fine-tuning variation
				const quaternaryFactor = Math.cos(index * 0.7) * 2; // Additional variation

				// Combine all factors for maximum size differences
				const combinedWeight =
					8 +
					primaryFactor +
					secondaryFactor +
					tertiaryFactor +
					quaternaryFactor;

				// Ensure minimum differences of at least 5px equivalent in weight
				const quantizedWeight = Math.round(combinedWeight * 2) / 2; // Round to 0.5 increments

				return {
					...item,
					weight: Math.max(3, Math.min(20, quantizedWeight)), // Range: 3-20 for extreme differences
				};
			});

			// Sort by weight to encourage more varied groupings
			subcategoriesWithWeights.sort((a, b) => a.weight - b.weight);

			// Generate treemap layout with dynamic container size
			const newLayoutCards = generateTreemap(
				subcategoriesWithWeights,
				0,
				0,
				containerSize.width,
				containerSize.height
			);

			setLayoutCards(newLayoutCards);
		}
	}, [shuffledData, selectedCategory, selectedSubcategory, containerSize]); // Include containerSize in dependencies

	// Get subcategories for selected category
	const getSubcategoriesForCategory = (category: string) => {
		if (category === 'all') return [];
		return allSubcategories
			.filter((item) => item.category === category)
			.map((item) => item.subcategory)
			.filter((value, index, self) => self.indexOf(value) === index); // Remove duplicates
	};

	const availableSubcategories = getSubcategoriesForCategory(selectedCategory);

	return (
		<div className='space-y-8'>
			{/* Enhanced Filter Design - 3 Categories + Subcategories */}
			<div
				className='flex flex-wrap justify-center items-center mb-6 sm:mb-8'
				style={{ gap: FILTER_RESPONSIVE_CONFIG.gap[screenSize] }}>
				{/* Show 3 main categories as cards */}
				<div
					className={`border-2 cursor-pointer transition-all duration-300 ${
						selectedCategory === 'all' ? 'scale-105' : 'hover:scale-105'
					}`}
					style={{
						padding: FILTER_RESPONSIVE_CONFIG.padding[screenSize],
						borderRadius: FILTER_RESPONSIVE_CONFIG.borderRadius[screenSize],
						fontSize: FILTER_RESPONSIVE_CONFIG.fontSize[screenSize],
					}}
					style={{
						borderColor:
							selectedCategory === 'all'
								? colors.brand.blue
								: colors.supporting.lightBlue,
						background:
							selectedCategory === 'all'
								? `linear-gradient(135deg, ${colors.brand.blue}20 0%, ${colors.brand.green}20 100%)`
								: `linear-gradient(135deg, ${colors.supporting.lightBlue}15 0%, ${colors.supporting.mintGreen}15 100%)`,
						color:
							selectedCategory === 'all'
								? colors.brand.navy
								: colors.supporting.darkBlue,
						fontWeight: selectedCategory === 'all' ? '700' : '600',
						boxShadow:
							selectedCategory === 'all'
								? `0 8px 25px ${colors.brand.blue}30`
								: `0 4px 15px ${colors.supporting.lightBlue}20`,
					}}
					onClick={() => {
						setSelectedCategory('all');
						setSelectedSubcategory('all');
					}}>
					<span>🌟 All Categories</span>
				</div>

				{allCategories.slice(0, 3).map((category) => (
					<div
						key={category}
						className={`border-2 cursor-pointer transition-all duration-300 ${
							selectedCategory === category ? 'scale-105' : 'hover:scale-105'
						}`}
						style={{
							padding: FILTER_RESPONSIVE_CONFIG.padding[screenSize],
							borderRadius: FILTER_RESPONSIVE_CONFIG.borderRadius[screenSize],
							fontSize: FILTER_RESPONSIVE_CONFIG.fontSize[screenSize],
						}}
						style={{
							borderColor:
								selectedCategory === category
									? colors.brand.green
									: colors.supporting.teal,
							background:
								selectedCategory === category
									? `linear-gradient(135deg, ${colors.brand.green}20 0%, ${colors.brand.blue}20 100%)`
									: `linear-gradient(135deg, ${colors.supporting.teal}15 0%, ${colors.supporting.lightBlue}15 100%)`,
							color:
								selectedCategory === category
									? colors.brand.navy
									: colors.supporting.darkBlue,
							fontWeight: selectedCategory === category ? '700' : '600',
							boxShadow:
								selectedCategory === category
									? `0 8px 25px ${colors.brand.green}30`
									: `0 4px 15px ${colors.supporting.teal}20`,
						}}
						onClick={() => {
							setSelectedCategory(category);
							setSelectedSubcategory('all');
						}}>
						<span>📍 {category}</span>
					</div>
				))}

				{/* Dropdown for additional categories */}
				{allCategories.length > 3 && (
					<div className='relative'>
						<select
							value={selectedCategory}
							onChange={(e) => {
								if (e.target.value !== selectedCategory) {
									setSelectedCategory(e.target.value);
									setSelectedSubcategory('all');
								}
							}}
							className='w-10 h-10 sm:w-12 sm:h-12 rounded-full border-2 cursor-pointer transition-all duration-300 hover:scale-110 appearance-none'
							style={{
								borderColor: colors.supporting.purple,
								background: `linear-gradient(135deg, ${colors.supporting.purple}20 0%, ${colors.supporting.mintGreen}20 100%)`,
								backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='${colors.supporting.darkBlue.replace(
									'#',
									'%23'
								)}'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3e%3c/path%3e%3c/svg%3e")`,
								backgroundPosition: 'center',
								backgroundSize: '16px 16px',
								backgroundRepeat: 'no-repeat',
								boxShadow: `0 4px 15px ${colors.supporting.purple}25`,
							}}>
							<option value=''>More</option>
							{allCategories.slice(3).map((category) => (
								<option
									key={category}
									value={category}>
									{category}
								</option>
							))}
						</select>
					</div>
				)}
			</div>

			{/* Subcategory Filter - Only show when specific category is selected */}
			{selectedCategory !== 'all' && (
				<div className='flex flex-wrap justify-center items-center gap-4 mb-8 animate-fadeIn'>
					<div
						className={`px-4 py-2 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
							selectedSubcategory === 'all' ? 'scale-105' : 'hover:scale-105'
						}`}
						style={{
							borderColor:
								selectedSubcategory === 'all'
									? colors.supporting.purple
									: colors.supporting.mintGreen,
							background:
								selectedSubcategory === 'all'
									? `linear-gradient(135deg, ${colors.supporting.purple}20 0%, ${colors.supporting.lightBlue}20 100%)`
									: `linear-gradient(135deg, ${colors.supporting.mintGreen}15 0%, ${colors.supporting.lightBlue}15 100%)`,
							color:
								selectedSubcategory === 'all'
									? colors.brand.navy
									: colors.supporting.darkBlue,
							fontWeight: selectedSubcategory === 'all' ? '600' : '500',
							fontSize: '14px',
							boxShadow:
								selectedSubcategory === 'all'
									? `0 6px 20px ${colors.supporting.purple}25`
									: `0 3px 10px ${colors.supporting.mintGreen}15`,
						}}
						onClick={() => setSelectedSubcategory('all')}>
						✨ All Subcategories
					</div>

					{availableSubcategories.map((subcategory) => (
						<div
							key={subcategory}
							className={`px-4 py-2 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
								selectedSubcategory === subcategory
									? 'scale-105'
									: 'hover:scale-105'
							}`}
							style={{
								borderColor:
									selectedSubcategory === subcategory
										? colors.supporting.darkBlue
										: colors.supporting.softNavy,
								background:
									selectedSubcategory === subcategory
										? `linear-gradient(135deg, ${colors.supporting.darkBlue}20 0%, ${colors.supporting.purple}20 100%)`
										: `linear-gradient(135deg, ${colors.supporting.softNavy}15 0%, ${colors.supporting.mintGreen}15 100%)`,
								color:
									selectedSubcategory === subcategory
										? colors.brand.navy
										: colors.supporting.darkBlue,
								fontWeight: selectedSubcategory === subcategory ? '600' : '500',
								fontSize: '14px',
								boxShadow:
									selectedSubcategory === subcategory
										? `0 6px 20px ${colors.supporting.darkBlue}25`
										: `0 3px 10px ${colors.supporting.softNavy}15`,
							}}
							onClick={() => setSelectedSubcategory(subcategory)}>
							🎯 {subcategory}
						</div>
					))}
				</div>
			)}

			{/* Responsive Card Container */}
			<div className='flex justify-center px-4'>
				<div
					ref={containerRef}
					className='w-full max-w-7xl'
					style={{
						width: '100%',
						maxWidth: 'min(1200px, calc(100vw - 2rem))',
						height: `${containerSize.height}px`,
						minHeight: '300px',
						position: 'relative',
						boxSizing: 'border-box',
						overflow: 'hidden',
						background: 'transparent',
						margin: '0 auto',
					}}>
					{layoutCards.map((card, index) => (
						<CategoryCard
							key={`${card.category}-${card.subcategory}-${index}`}
							cardData={card}
							index={index}
						/>
					))}
				</div>
			</div>
		</div>
	);
};

export default DynamicCategoryMosaic;
